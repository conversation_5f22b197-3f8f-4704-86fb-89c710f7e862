{"name": "synapseai", "version": "1.0.0", "description": "Universal AI orchestration platform with click-configurable agents, tools, and real-time WebSocket protocol", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=apps/backend", "dev:frontend": "npm run dev --workspace=apps/frontend", "build": "npm run build --workspaces", "build:backend": "npm run build --workspace=apps/backend", "build:frontend": "npm run build --workspace=apps/frontend", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "npm run start --workspace=apps/backend", "start:frontend": "npm run start --workspace=apps/frontend", "test": "npm run test --workspaces", "test:backend": "npm run test --workspace=apps/backend", "test:frontend": "npm run test --workspace=apps/frontend", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "clean": "rimraf node_modules apps/*/node_modules packages/*/node_modules apps/*/dist packages/*/dist", "typecheck": "npm run typecheck --workspaces", "db:migrate": "npm run db:migrate --workspace=apps/backend", "db:seed": "npm run db:seed --workspace=apps/backend", "db:reset": "npm run db:reset --workspace=apps/backend"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "rimraf": "^5.0.5", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["ai", "agents", "tools", "websocket", "<PERSON><PERSON><PERSON>", "nextjs", "typescript", "orchestration", "automation"], "author": "SynapseAI Team", "license": "MIT"}