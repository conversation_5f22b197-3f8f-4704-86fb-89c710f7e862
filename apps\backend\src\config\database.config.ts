import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { DataSource, DataSourceOptions } from 'typeorm';
import 'dotenv/config'; // Enables .env for CLI

// Import all entities explicitly
import { User } from '../database/entities/user.entity';
import { Organization } from '../database/entities/organization.entity';
import { Agent } from '../database/entities/agent.entity';
import { Session } from '../database/entities/session.entity';
import { Message } from '../database/entities/message.entity';
import { Tool } from '../database/entities/tool.entity';
import { Provider } from '../database/entities/provider.entity';
import { KnowledgeBase } from '../database/entities/knowledge-base.entity';
import { Document } from '../database/entities/document.entity';
import { AnalyticsEvent } from '../database/entities/analytics-event.entity';

export const databaseConfig = (
  configService: ConfigService,
): TypeOrmModuleOptions => ({
  type: 'postgres',
  host: configService.get('DATABASE_HOST', 'localhost'),
  port: parseInt(configService.get('DATABASE_PORT', '5432')),
  username: configService.get('DATABASE_USERNAME', 'postgres'),
  password: configService.get('DATABASE_PASSWORD', ''),
  database: configService.get('DATABASE_NAME', 'synapseai'),
  entities: [
    User,
    Organization,
    Agent,
    Session,
    Message,
    Tool,
    Provider,
    KnowledgeBase,
    Document,
    AnalyticsEvent,
  ],
  migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
  synchronize: false, // Always false for safety
  logging: configService.get('NODE_ENV') === 'development',
});


// For TypeORM CLI
export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '5432'),
  username: process.env.DATABASE_USERNAME || 'postgres',
  password: '', // ✅ No password required
  database: process.env.DATABASE_NAME || 'synapseai',
  entities: [
    User,
    Organization,
    Agent,
    Session,
    Message,
    Tool,
    Provider,
    KnowledgeBase,
    Document,
    AnalyticsEvent,
  ],
  migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
  synchronize: false,
  logging: process.env.NODE_ENV === 'development',
};

const dataSource = new DataSource(dataSourceOptions);
export default dataSource;
