{"name": "@synapseai/frontend", "version": "1.0.0", "description": "SynapseAI Frontend - Next.js 14 with App Router", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-separator": "^1.0.3", "zustand": "^4.4.6", "socket.io-client": "^4.7.0", "axios": "^1.6.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-query": "^3.39.3", "@tanstack/react-query": "^5.8.0", "framer-motion": "^10.16.0", "react-hot-toast": "^2.4.1", "next-themes": "^0.2.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-dropzone": "^14.2.3", "react-beautiful-dnd": "^13.1.1"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-syntax-highlighter": "^15.5.9", "@types/react-beautiful-dnd": "^13.1.6", "typescript": "^5.2.0", "eslint": "^8.51.0", "eslint-config-next": "14.0.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.6", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "jest-environment-jsdom": "^29.7.0"}}