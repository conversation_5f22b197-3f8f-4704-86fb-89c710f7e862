import { Injectable, Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { ConfigService } from '@nestjs/config';

import { SessionsService } from '../sessions/sessions.service';
import { 
  UserMessageEvent,
  UserResponseEvent,
  ControlSignalEvent,
} from './types/apix-events';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  organizationId?: string;
  sessionId?: string;
}

@Injectable()
export class WebSocketService {
  private server: Server;
  private readonly logger = new Logger(WebSocketService.name);
  private connectedClients = new Map<string, AuthenticatedSocket>();

  constructor(
    private readonly sessionsService: SessionsService,
    private readonly configService: ConfigService,
  ) {}

  setServer(server: Server) {
    this.server = server;
  }

  async handleConnection(client: AuthenticatedSocket): Promise<void> {
    this.connectedClients.set(client.id, client);
    
    // Send welcome message
    client.emit('connected', {
      clientId: client.id,
      timestamp: Date.now(),
      message: 'Connected to SynapseAI APIX Gateway',
    });

    this.logger.log(`Client ${client.id} connected (User: ${client.userId})`);
  }

  async handleDisconnection(client: AuthenticatedSocket): Promise<void> {
    this.connectedClients.delete(client.id);
    
    // Update session activity if client was in a session
    if (client.sessionId) {
      try {
        await this.sessionsService.updateActivity(client.sessionId);
      } catch (error) {
        this.logger.error(`Failed to update session activity: ${error.message}`);
      }
    }

    this.logger.log(`Client ${client.id} disconnected`);
  }

  async handleUserMessage(
    client: AuthenticatedSocket,
    data: UserMessageEvent['data'],
  ): Promise<void> {
    try {
      this.logger.log(`User message from ${client.userId}: ${data.message}`);

      // Emit thinking status
      client.emit('thinking_status', {
        status: 'thinking',
        message: 'Processing your message...',
        progress: 0,
      });

      // Simulate AI processing with streaming response
      await this.simulateAIResponse(client, data.message);

    } catch (error) {
      this.logger.error(`Error handling user message: ${error.message}`);
      client.emit('error', {
        error: 'Failed to process message',
        code: 'MESSAGE_PROCESSING_ERROR',
        recoverable: true,
      });
    }
  }

  private async simulateAIResponse(client: AuthenticatedSocket, message: string): Promise<void> {
    const messageId = `msg_${Date.now()}`;

    // Simulate thinking
    client.emit('thinking_status', {
      status: 'processing',
      message: 'Analyzing your request...',
      progress: 25,
    });

    await this.sleep(500);

    // Simulate streaming response
    const response = `I understand you said: "${message}". This is a simulated AI response that demonstrates the APIX protocol with streaming text chunks.`;
    const words = response.split(' ');

    for (let i = 0; i < words.length; i++) {
      const chunk = words[i] + ' ';
      const isComplete = i === words.length - 1;

      client.emit('text_chunk', {
        chunk,
        isComplete,
        messageId,
      });

      // Small delay between chunks to simulate streaming
      await this.sleep(50);
    }

    // Complete the response
    client.emit('thinking_status', {
      status: 'complete',
      message: 'Response completed',
      progress: 100,
    });
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async handleUserResponse(
    client: AuthenticatedSocket,
    data: UserResponseEvent['data'],
  ): Promise<void> {
    try {
      this.logger.log(`User response from ${client.userId}: ${data.response}`);
      
      // TODO: Handle HITL response
      // This will be implemented in the HITL task
      
    } catch (error) {
      this.logger.error(`Error handling user response: ${error.message}`);
      client.emit('error', {
        error: 'Failed to process response',
        code: 'RESPONSE_PROCESSING_ERROR',
        recoverable: true,
      });
    }
  }

  async handleControlSignal(
    client: AuthenticatedSocket,
    data: ControlSignalEvent['data'],
  ): Promise<void> {
    try {
      this.logger.log(`Control signal from ${client.userId}: ${data.action}`);

      switch (data.action) {
        case 'stop':
          client.emit('state_update', {
            state: { status: 'stopped', reason: data.reason || 'User requested stop' },
          });
          break;
        case 'pause':
          client.emit('state_update', {
            state: { status: 'paused', reason: data.reason || 'User requested pause' },
          });
          break;
        case 'resume':
          client.emit('state_update', {
            state: { status: 'active', reason: 'User requested resume' },
          });
          break;
        case 'restart':
          client.emit('state_update', {
            state: { status: 'restarting', reason: 'User requested restart' },
          });
          // Simulate restart
          await this.sleep(1000);
          client.emit('state_update', {
            state: { status: 'active', reason: 'Restart completed' },
          });
          break;
        default:
          throw new Error(`Unknown control action: ${data.action}`);
      }

    } catch (error) {
      this.logger.error(`Error handling control signal: ${error.message}`);
      client.emit('error', {
        error: 'Failed to process control signal',
        code: 'CONTROL_SIGNAL_ERROR',
        recoverable: true,
      });
    }
  }

  async joinSession(client: AuthenticatedSocket, sessionId: string): Promise<void> {
    try {
      // Verify session exists and user has access
      // TODO: Add session validation
      
      client.sessionId = sessionId;
      await client.join(`session:${sessionId}`);
      
      client.emit('session_joined', {
        sessionId,
        timestamp: Date.now(),
      });

      this.logger.log(`Client ${client.id} joined session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Error joining session: ${error.message}`);
      client.emit('error', {
        error: 'Failed to join session',
        code: 'SESSION_JOIN_ERROR',
        recoverable: true,
      });
    }
  }

  async leaveSession(client: AuthenticatedSocket, sessionId: string): Promise<void> {
    try {
      await client.leave(`session:${sessionId}`);
      client.sessionId = undefined;
      
      client.emit('session_left', {
        sessionId,
        timestamp: Date.now(),
      });

      this.logger.log(`Client ${client.id} left session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Error leaving session: ${error.message}`);
    }
  }

  // Utility methods for broadcasting to sessions
  broadcastToSession(sessionId: string, event: string, data: any): void {
    this.server.to(`session:${sessionId}`).emit(event, data);
  }

  broadcastToUser(userId: string, event: string, data: any): void {
    // Find all sockets for this user
    for (const [clientId, client] of this.connectedClients) {
      if (client.userId === userId) {
        client.emit(event, data);
      }
    }
  }

  broadcastToOrganization(organizationId: string, event: string, data: any): void {
    // Find all sockets for this organization
    for (const [clientId, client] of this.connectedClients) {
      if (client.organizationId === organizationId) {
        client.emit(event, data);
      }
    }
  }

  getConnectedClients(): number {
    return this.connectedClients.size;
  }

  getClientsByOrganization(organizationId: string): number {
    let count = 0;
    for (const [clientId, client] of this.connectedClients) {
      if (client.organizationId === organizationId) {
        count++;
      }
    }
    return count;
  }
}
