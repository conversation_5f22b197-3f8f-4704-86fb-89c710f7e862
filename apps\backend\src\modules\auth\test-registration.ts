// Temporary test file to debug registration issues
import { DataSource } from 'typeorm';
import { Organization, OrganizationStatus } from '../../database/entities/organization.entity';
import { User, UserStatus } from '../../database/entities/user.entity';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

export async function testRegistration(dataSource: DataSource) {
  console.log('🔍 Testing registration process...');
  
  try {
    // Test database connection
    console.log('1. Testing database connection...');
    await dataSource.query('SELECT 1');
    console.log('✅ Database connection successful');

    // Test organization table structure
    console.log('2. Checking organization table structure...');
    const orgColumns = await dataSource.query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'organizations' 
      ORDER BY ordinal_position
    `);
    console.log('Organization table columns:', orgColumns);

    // Test user table structure
    console.log('3. Checking user table structure...');
    const userColumns = await dataSource.query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `);
    console.log('User table columns:', userColumns);

    // Test creating organization
    console.log('4. Testing organization creation...');
    const orgRepo = dataSource.getRepository(Organization);
    
    // Check if default organization exists
    let defaultOrg = await orgRepo.findOne({ where: { slug: 'default' } });
    
    if (!defaultOrg) {
      console.log('Creating default organization...');
      defaultOrg = orgRepo.create({
        name: 'Default Organization',
        slug: 'default',
        status: OrganizationStatus.ACTIVE,
      });
      defaultOrg = await orgRepo.save(defaultOrg);
      console.log('✅ Default organization created:', defaultOrg.id);
    } else {
      console.log('✅ Default organization exists:', defaultOrg.id);
    }

    // Test creating user
    console.log('5. Testing user creation...');
    const userRepo = dataSource.getRepository(User);
    
    // Check if test user exists
    const testEmail = '<EMAIL>';
    let existingUser = await userRepo.findOne({ where: { email: testEmail } });
    
    if (existingUser) {
      console.log('Removing existing test user...');
      await userRepo.remove(existingUser);
    }

    const hashedPassword = await bcrypt.hash('TestPass123', 12);
    
    const testUser = userRepo.create({
      email: testEmail,
      password: hashedPassword,
      firstName: 'Test',
      lastName: 'User',
      organizationId: defaultOrg.id,
      status: UserStatus.ACTIVE,
      emailVerificationToken: uuidv4(),
    });

    const savedUser = await userRepo.save(testUser);
    console.log('✅ Test user created:', savedUser.id);

    // Clean up
    console.log('6. Cleaning up test data...');
    await userRepo.remove(savedUser);
    console.log('✅ Test user removed');

    console.log('🎉 All tests passed! Registration should work.');
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}
