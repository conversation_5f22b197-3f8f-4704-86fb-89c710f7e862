import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Organization, OrganizationStatus, OrganizationPlan } from '../../database/entities/organization.entity';
import { User, UserRole } from '../../database/entities/user.entity';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';

@Injectable()
export class OrganizationsService {
  constructor(
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(createOrganizationDto: CreateOrganizationDto, currentUser: User): Promise<Organization> {
    // Only super admins can create organizations
    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Only super admins can create organizations');
    }

    const { name, slug, ...organizationData } = createOrganizationDto;

    // Check if slug already exists
    const existingOrg = await this.organizationRepository.findOne({
      where: { slug },
    });

    if (existingOrg) {
      throw new ConflictException('Organization with this slug already exists');
    }

    // Create organization
    const organization = this.organizationRepository.create({
      name,
      slug,
      ...organizationData,
      status: OrganizationStatus.TRIAL,
      plan: OrganizationPlan.FREE,
      trialEndsAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days trial
    });

    return this.organizationRepository.save(organization);
  }

  async findAll(currentUser: User): Promise<Organization[]> {
    // Only super admins can see all organizations
    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Only super admins can view all organizations');
    }

    return this.organizationRepository.find({
      relations: ['users'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string, currentUser: User): Promise<Organization> {
    // Users can only see their own organization, super admins can see all
    const whereCondition = currentUser.role === UserRole.SUPER_ADMIN 
      ? { id } 
      : { id, id: currentUser.organizationId };

    const organization = await this.organizationRepository.findOne({
      where: whereCondition,
      relations: ['users'],
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return organization;
  }

  async findBySlug(slug: string): Promise<Organization | null> {
    return this.organizationRepository.findOne({
      where: { slug },
      relations: ['users'],
    });
  }

  async update(id: string, updateOrganizationDto: UpdateOrganizationDto, currentUser: User): Promise<Organization> {
    const organization = await this.findOne(id, currentUser);

    // Check permissions
    if (currentUser.role !== UserRole.SUPER_ADMIN && currentUser.organizationId !== id) {
      throw new ForbiddenException('Cannot update other organizations');
    }

    // Only super admins can change status and plan
    if ((updateOrganizationDto.status || updateOrganizationDto.plan) && currentUser.role !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Only super admins can change organization status or plan');
    }

    // Check slug uniqueness if being updated
    if (updateOrganizationDto.slug && updateOrganizationDto.slug !== organization.slug) {
      const existingOrg = await this.organizationRepository.findOne({
        where: { slug: updateOrganizationDto.slug },
      });

      if (existingOrg) {
        throw new ConflictException('Organization with this slug already exists');
      }
    }

    Object.assign(organization, updateOrganizationDto);
    return this.organizationRepository.save(organization);
  }

  async remove(id: string, currentUser: User): Promise<void> {
    // Only super admins can delete organizations
    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Only super admins can delete organizations');
    }

    const organization = await this.organizationRepository.findOne({
      where: { id },
      relations: ['users'],
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Prevent deleting organization with users
    if (organization.users && organization.users.length > 0) {
      throw new BadRequestException('Cannot delete organization with existing users');
    }

    await this.organizationRepository.remove(organization);
  }

  async getStats(id: string, currentUser: User): Promise<{
    userCount: number;
    activeUsers: number;
    trialDaysRemaining: number;
    storageUsed: number;
    apiCallsThisMonth: number;
  }> {
    const organization = await this.findOne(id, currentUser);

    const userCount = await this.userRepository.count({
      where: { organizationId: id },
    });

    const activeUsers = await this.userRepository.count({
      where: { organizationId: id, status: 'active' },
    });

    return {
      userCount,
      activeUsers,
      trialDaysRemaining: organization.trialDaysRemaining,
      storageUsed: 0, // TODO: Calculate actual storage usage
      apiCallsThisMonth: 0, // TODO: Calculate from analytics
    };
  }

  async updateLimits(id: string, limits: any, currentUser: User): Promise<Organization> {
    // Only super admins can update limits
    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Only super admins can update organization limits');
    }

    const organization = await this.organizationRepository.findOne({
      where: { id },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    organization.limits = { ...organization.limits, ...limits };
    return this.organizationRepository.save(organization);
  }

  async updateBilling(id: string, billing: any, currentUser: User): Promise<Organization> {
    // Only super admins can update billing
    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Only super admins can update billing information');
    }

    const organization = await this.organizationRepository.findOne({
      where: { id },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    organization.billing = { ...organization.billing, ...billing };
    return this.organizationRepository.save(organization);
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
}
