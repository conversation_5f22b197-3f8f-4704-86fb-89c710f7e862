import React from 'react';
import { calculatePasswordStrength } from '@/lib/validation';
import { cn } from '@/lib/utils';

interface PasswordStrengthProps {
  password: string;
  className?: string;
  showFeedback?: boolean;
}

export function PasswordStrength({ 
  password, 
  className,
  showFeedback = true 
}: PasswordStrengthProps) {
  const { score, level, feedback } = calculatePasswordStrength(password);

  if (!password) {
    return null;
  }

  const getStrengthColor = (level: string) => {
    switch (level) {
      case 'weak':
        return 'bg-red-500';
      case 'fair':
        return 'bg-yellow-500';
      case 'good':
        return 'bg-blue-500';
      case 'strong':
        return 'bg-green-500';
      default:
        return 'bg-gray-300';
    }
  };

  const getStrengthText = (level: string) => {
    switch (level) {
      case 'weak':
        return 'Weak';
      case 'fair':
        return 'Fair';
      case 'good':
        return 'Good';
      case 'strong':
        return 'Strong';
      default:
        return '';
    }
  };

  const getTextColor = (level: string) => {
    switch (level) {
      case 'weak':
        return 'text-red-600';
      case 'fair':
        return 'text-yellow-600';
      case 'good':
        return 'text-blue-600';
      case 'strong':
        return 'text-green-600';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      {/* Strength Bar */}
      <div className="flex items-center space-x-2">
        <div className="flex-1 bg-gray-200 rounded-full h-2">
          <div
            className={cn(
              'h-2 rounded-full transition-all duration-300',
              getStrengthColor(level)
            )}
            style={{ width: `${score}%` }}
          />
        </div>
        <span className={cn('text-sm font-medium', getTextColor(level))}>
          {getStrengthText(level)}
        </span>
      </div>

      {/* Feedback */}
      {showFeedback && feedback.length > 0 && (
        <div className="space-y-1">
          <p className="text-xs text-muted-foreground">
            To strengthen your password:
          </p>
          <ul className="text-xs text-muted-foreground space-y-1">
            {feedback.map((item, index) => (
              <li key={index} className="flex items-center space-x-1">
                <span className="w-1 h-1 bg-muted-foreground rounded-full" />
                <span>{item}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

// Password requirements checklist component
interface PasswordRequirementsProps {
  password: string;
  requirements?: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSpecialChars?: boolean;
  };
  className?: string;
}

export function PasswordRequirements({ 
  password, 
  requirements = {},
  className 
}: PasswordRequirementsProps) {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = false,
  } = requirements;

  const checks = [
    {
      label: `At least ${minLength} characters`,
      met: password.length >= minLength,
    },
    {
      label: 'One lowercase letter',
      met: /[a-z]/.test(password),
      required: requireLowercase,
    },
    {
      label: 'One uppercase letter',
      met: /[A-Z]/.test(password),
      required: requireUppercase,
    },
    {
      label: 'One number',
      met: /\d/.test(password),
      required: requireNumbers,
    },
    {
      label: 'One special character',
      met: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      required: requireSpecialChars,
    },
  ].filter(check => check.required !== false);

  if (!password) {
    return null;
  }

  return (
    <div className={cn('space-y-2', className)}>
      <p className="text-sm font-medium text-muted-foreground">
        Password Requirements:
      </p>
      <ul className="space-y-1">
        {checks.map((check, index) => (
          <li key={index} className="flex items-center space-x-2 text-sm">
            <div
              className={cn(
                'w-4 h-4 rounded-full flex items-center justify-center',
                check.met
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-200 text-gray-400'
              )}
            >
              {check.met ? (
                <svg
                  className="w-3 h-3"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                <svg
                  className="w-3 h-3"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </div>
            <span
              className={cn(
                check.met ? 'text-green-600' : 'text-muted-foreground'
              )}
            >
              {check.label}
            </span>
          </li>
        ))}
      </ul>
    </div>
  );
}
