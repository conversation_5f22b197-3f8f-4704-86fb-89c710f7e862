import { toast } from '@/hooks/use-toast';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastOptions {
  title: string;
  description?: string;
  duration?: number;
  action?: React.ReactNode;
}

// Enhanced toast utilities with color-coded notifications
export const toastUtils = {
  success: (options: ToastOptions) => {
    toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 5000,
      action: options.action,
      className: 'border-green-200 bg-green-50 text-green-900',
    });
  },

  error: (options: ToastOptions) => {
    toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 7000,
      action: options.action,
      variant: 'destructive',
      className: 'border-red-200 bg-red-50 text-red-900',
    });
  },

  warning: (options: ToastOptions) => {
    toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 6000,
      action: options.action,
      className: 'border-yellow-200 bg-yellow-50 text-yellow-900',
    });
  },

  info: (options: ToastOptions) => {
    toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 5000,
      action: options.action,
      className: 'border-blue-200 bg-blue-50 text-blue-900',
    });
  },

  // Form-specific toast messages
  form: {
    validationError: (message: string) => {
      toastUtils.error({
        title: 'Validation Error',
        description: message,
        duration: 5000,
      });
    },

    submitError: (message: string) => {
      toastUtils.error({
        title: 'Submission Failed',
        description: message,
        duration: 7000,
      });
    },

    submitSuccess: (message: string) => {
      toastUtils.success({
        title: 'Success!',
        description: message,
        duration: 5000,
      });
    },

    loading: (message: string) => {
      toastUtils.info({
        title: 'Processing...',
        description: message,
        duration: 10000,
      });
    },
  },

  // Authentication-specific toast messages
  auth: {
    loginSuccess: (userName?: string) => {
      toastUtils.success({
        title: 'Welcome back!',
        description: userName ? `Hello ${userName}, you're successfully logged in.` : 'You have been successfully logged in.',
        duration: 4000,
      });
    },

    loginError: (message: string) => {
      toastUtils.error({
        title: 'Login Failed',
        description: message,
        duration: 6000,
      });
    },

    registerSuccess: () => {
      toastUtils.success({
        title: 'Account Created!',
        description: 'Your account has been successfully created. Welcome to SynapseAI!',
        duration: 5000,
      });
    },

    registerError: (message: string) => {
      toastUtils.error({
        title: 'Registration Failed',
        description: message,
        duration: 6000,
      });
    },

    logoutSuccess: () => {
      toastUtils.info({
        title: 'Logged Out',
        description: 'You have been successfully logged out.',
        duration: 3000,
      });
    },

    sessionExpired: () => {
      toastUtils.warning({
        title: 'Session Expired',
        description: 'Your session has expired. Please log in again.',
        duration: 5000,
      });
    },

    passwordResetSent: (email: string) => {
      toastUtils.success({
        title: 'Reset Link Sent',
        description: `Password reset instructions have been sent to ${email}.`,
        duration: 6000,
      });
    },

    passwordResetSuccess: () => {
      toastUtils.success({
        title: 'Password Updated',
        description: 'Your password has been successfully updated.',
        duration: 4000,
      });
    },

    emailVerificationSent: (email: string) => {
      toastUtils.info({
        title: 'Verification Email Sent',
        description: `Please check ${email} for verification instructions.`,
        duration: 6000,
      });
    },
  },
};

// Server error message mapping to user-friendly messages
export const errorMessageMap: Record<string, string> = {
  // Authentication errors
  'Invalid credentials': 'The email or password you entered is incorrect. Please try again.',
  'User not found': 'No account found with this email address. Please check your email or create a new account.',
  'Email already exists': 'An account with this email address already exists. Please try logging in instead.',
  'Invalid email': 'Please enter a valid email address.',
  'Password too weak': 'Your password is too weak. Please choose a stronger password.',
  'Token expired': 'This link has expired. Please request a new one.',
  'Invalid token': 'This link is invalid or has already been used.',
  
  // Network errors
  'Network Error': 'Unable to connect to the server. Please check your internet connection and try again.',
  'Request timeout': 'The request took too long to complete. Please try again.',
  
  // Server errors
  'Internal Server Error': 'Something went wrong on our end. Please try again in a few moments.',
  'Service Unavailable': 'The service is temporarily unavailable. Please try again later.',
  'Bad Gateway': 'There was a problem connecting to our servers. Please try again.',
  
  // Validation errors
  'Validation failed': 'Please check your input and try again.',
  'Required field missing': 'Please fill in all required fields.',
  'Invalid format': 'Please check the format of your input.',
};

// Function to get user-friendly error message
export function getUserFriendlyErrorMessage(error: any): string {
  // Extract error message from different error formats
  let errorMessage = '';
  
  if (typeof error === 'string') {
    errorMessage = error;
  } else if (error?.response?.data?.message) {
    errorMessage = error.response.data.message;
  } else if (error?.message) {
    errorMessage = error.message;
  } else if (error?.error) {
    errorMessage = error.error;
  } else {
    errorMessage = 'An unexpected error occurred';
  }

  // Return mapped user-friendly message or original message
  return errorMessageMap[errorMessage] || errorMessage;
}

// Function to handle API errors with appropriate toast notifications
export function handleApiError(error: any, context?: string) {
  const userFriendlyMessage = getUserFriendlyErrorMessage(error);
  
  if (context) {
    toastUtils.error({
      title: `${context} Failed`,
      description: userFriendlyMessage,
    });
  } else {
    toastUtils.error({
      title: 'Error',
      description: userFriendlyMessage,
    });
  }
}
